import 'package:flutter/material.dart';
import 'date_tile.dart';

class DateScroller extends StatefulWidget {
  const DateScroller({super.key});

  @override
  State<DateScroller> createState() => _DateScrollerState();
}

class _DateScrollerState extends State<DateScroller> {
  late DateTime selectedDate;
  late List<DateTime> dates;
  late ScrollController scrollController;

  @override
  void initState() {
    super.initState();
    selectedDate = DateTime.now();
    dates = _generateDates();
    scrollController = ScrollController();
    
    // Scroll to today's date after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedDate();
    });
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  List<DateTime> _generateDates() {
    final now = DateTime.now();
    final List<DateTime> dateList = [];
    
    // Generate the last 30 days
    for (int i = 29; i >= 0; i--) {
      dateList.add(DateTime(now.year, now.month, now.day - i));
    }
    
    return dateList;
  }

  void _scrollToSelectedDate() {
    final selectedIndex = dates.indexWhere((date) =>
        date.year == selectedDate.year &&
        date.month == selectedDate.month &&
        date.day == selectedDate.day);
    
    if (selectedIndex != -1) {
      final scrollPosition = selectedIndex * 68.0; // 60 width + 8 margin
      scrollController.animateTo(
        scrollPosition,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _onDateSelected(DateTime date) {
    setState(() {
      selectedDate = date;
    });
    _scrollToSelectedDate();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: ListView.builder(
        controller: scrollController,
        scrollDirection: Axis.horizontal,
        itemCount: dates.length,
        itemBuilder: (context, index) {
          final date = dates[index];
          final isSelected = date.year == selectedDate.year &&
                           date.month == selectedDate.month &&
                           date.day == selectedDate.day;
          
          return DateTile(
            date: date,
            isSelected: isSelected,
            onTap: () => _onDateSelected(date),
          );
        },
      ),
    );
  }
}
