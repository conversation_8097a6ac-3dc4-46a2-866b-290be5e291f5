import 'package:flutter/material.dart';
import 'habit.dart';
import 'habit_tile.dart';
import 'date_scroller.dart';

class HabitsScreen extends StatefulWidget {
  const HabitsScreen({super.key});

  @override
  State<HabitsScreen> createState() => _HabitsScreenState();
}

class _HabitsScreenState extends State<HabitsScreen> {
  // Hardcoded temporary list of habits
  List<Habit> habits = [
    Habit(name: "Read for 15 minutes", isCompleted: true),
    Habit(name: "Drink 8 glasses of water", isCompleted: false),
    Habit(name: "Exercise for 30 minutes", isCompleted: false),
  ];

  void habitToggled(int index) {
    setState(() {
      habits[index].isCompleted = !habits[index].isCompleted;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("My Habits"),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // Empty function for now
            },
          ),
        ],
        bottom: const PreferredSize(
          preferredSize: Size.fromHeight(60.0),
          child: DateScroller(),
        ),
      ),
      body: ListView.builder(
        itemCount: habits.length,
        itemBuilder: (context, index) {
          return HabitTile(
            habitName: habits[index].name,
            isCompleted: habits[index].isCompleted,
            onToggle: (value) => habitToggled(index),
          );
        },
      ),
    );
  }
}
