import 'package:flutter/material.dart';

class HabitTile extends StatelessWidget {
  final String habitName;
  final bool isCompleted;
  final Function(bool?) onToggle;

  const HabitTile({
    super.key,
    required this.habitName,
    required this.isCompleted,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Checkbox(value: isCompleted, onChanged: onToggle),
      title: Text(habitName),
    );
  }
}
