import 'package:flutter/material.dart';

class DateTile extends StatelessWidget {
  final DateTime date;
  final bool isSelected;
  final VoidCallback onTap;

  const DateTile({
    super.key,
    required this.date,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final isToday = date.year == now.year && 
                   date.month == now.month && 
                   date.day == now.day;
    final isPast = date.isBefore(DateTime(now.year, now.month, now.day));
    
    // Get day of week abbreviation
    final dayOfWeek = _getDayOfWeekAbbreviation(date.weekday);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60,
        margin: const EdgeInsets.symmetric(horizontal: 4.0),
        decoration: BoxDecoration(
          color: isSelected 
              ? Theme.of(context).colorScheme.primary
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: isToday && !isSelected
              ? Border.all(
                  color: Theme.of(context).colorScheme.primary,
                  width: 2,
                )
              : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              dayOfWeek,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: _getTextColor(context, isSelected, isPast, isToday),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              date.day.toString(),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _getTextColor(context, isSelected, isPast, isToday),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getDayOfWeekAbbreviation(int weekday) {
    switch (weekday) {
      case 1:
        return 'MON';
      case 2:
        return 'TUE';
      case 3:
        return 'WED';
      case 4:
        return 'THU';
      case 5:
        return 'FRI';
      case 6:
        return 'SAT';
      case 7:
        return 'SUN';
      default:
        return '';
    }
  }

  Color _getTextColor(BuildContext context, bool isSelected, bool isPast, bool isToday) {
    if (isSelected) {
      return Theme.of(context).colorScheme.onPrimary;
    }
    
    if (isToday) {
      return Theme.of(context).colorScheme.primary;
    }
    
    if (isPast) {
      return Theme.of(context).colorScheme.onSurface.withOpacity(0.6);
    }
    
    return Theme.of(context).colorScheme.onSurface;
  }
}
